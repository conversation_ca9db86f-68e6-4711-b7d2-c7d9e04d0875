<template>
  <ScrollContainer ref="wrapperRef" v-loading="loading">
    <div class="pages">
      <BasicForm @register="registerForm">
        <template #flowAduit>
          <FlowAudit templatedId="679244695498412101" layOut="horizontal"></FlowAudit>
        </template>
          <template #startTime="{ model, field }">
          <range-picker v-model:value="model[field]" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
        </template>
      </BasicForm>
      <div class="anchorGroup">
        <div
          v-for="(item, index) in anchorLinks"
          :key="item.href"
          :class="['anchorGroup-item', anchorKey == item.href ? 'anchorActive' : '']"
          @click="handleClickAnchor(item.href)">
          {{ item.title }}
        </div>
      </div>
    </div>
  </ScrollContainer>
</template>

<script lang="ts" setup>
  import { ref, computed, onMounted, onUnmounted } from 'vue';
  import { BasicForm, useForm } from '@/components/Form';
  import { ScrollContainer } from '@/components/Container';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useBaseStore } from '@/store/modules/base';
  import { useI18n } from '@/hooks/web/useI18n';
  import { getDictionaryDataInfo as getInfo, createDictionaryData as create, updateDictionaryData as update } from '@/api/systemData/dictionary';
  import { useDebounceFn } from '@vueuse/core';
  import FlowAudit from '@/views/common/flowAudit/index.vue';
  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    schemas: [
      {
        field: 'form-a1',
        label: '',
        component: 'GroupTitle',
        componentProps: { id: 'form-a1', content: '申请时间' },
      },
      {
        field: 'academicYear',
        label: '学年',
        component: 'Select',
        componentProps: { options: [], placeholder: '请选择' },
        rules: [{ required: true, message: '请选择学年' }],
      },
      {
        field: 'applicationStartDate',
        label: '申请开始日期',
        component: 'DatePicker',
        componentProps: { picker: 'date', placeholder: '请选择日期' },
        rules: [{ required: true, message: '请选择日期' }],
      },
      {
        field: 'applicationEndDate',
        label: '申请结束日期',
        component: 'DatePicker',
        componentProps: { picker: 'date', placeholder: '请选择日期' },
        rules: [{ required: true, message: '请选择日期' }],
      },
      {
        field: 'form-a2',
        label: '',
        component: 'GroupTitle',
        componentProps: { id: 'form-a2', content: '审核截止时间' },
      },
      {
        field: 'reviewDeadline',
        label: '院系审核截止日期',
        component: 'DatePicker',
        componentProps: { picker: 'date', placeholder: '请选择日期' },
      },
      {
        field: 'form-a3',
        label: '',
        component: 'GroupTitle',
        componentProps: { id: 'form-a3', content: '公示信息' },
      },
      {
        field: 'isSchoolPublic',
        label: '校级公示',
        component: 'Switch',
        componentProps: {},
      },
      {
        field: 'schoolPublicStartDate',
        label: '校级公示日期',
        component: 'DatePicker',
        ifShow: ({ values }) => values.isSchoolPublic == true,
        componentProps: { picker: 'date', placeholder: '请选择日期' },
        rules: [{ required: true, message: '请选择日期' }],
        slot: 'schoolPublicStartDate',
      },

      {
        field: 'isCollegePublic',
        label: '学院公示',
        component: 'Switch',
        componentProps: {},
      },
      {
        field: 'collegePublicStartDate',
        label: '学院公示日期',
        component: 'DatePicker',
        slot: 'collegePublicStartDate',
        rules: [{ required: true, message: '请选择日期' }],
      },

      {
        field: 'isClassPublic',
        label: '班级公示',
        component: 'Switch',
        componentProps: {},
      },
      {
        field: 'classPublicStartDate',
        label: '班级公示日期',
        component: 'DatePicker',
        componentProps: { picker: 'date', placeholder: '请选择日期' },
        ifShow: ({ values }) => values.isClassPublic == true,
        rules: [{ required: true, message: '请选择日期' }],
        slot: 'classPublicStartDate',
      },

      {
        field: 'form-a4',
        label: '',
        component: 'GroupTitle',
        componentProps: { id: 'form-a4', content: '审核流程设置' },
      },
      {
        field: 'flowAduit',
        label: '',
        component: 'Input',
        slot: 'flowAduit',
      },
    ],
    labelWidth: 130,
  });
  const loading = ref(false);
  const id = ref('');
  const { createMessage } = useMessage();
  const { t } = useI18n();

  const getTitle = computed(() => (!unref(id) ? '新建' : '编辑'));

  async function getOptions() {
    const academicYears = await getBaseStore().getDictionaryData('academicYear');
    updateSchema({ field: 'academicYear', componentProps: { options: academicYears } });
  }

  async function handleSubmit() {
    const values = await validate();
    if (!values) return;

    // const query = {
    //   ...values,
    //   id: id.value,
    // };
    // const formMethod = id.value ? update : create;
    // formMethod(query)
    //   .then(res => {
    //     createMessage.success(res.msg);
    //     changeOkLoading(false);
    //     closePopup();
    //     emit('reload');
    //   })
    //   .catch(() => {

    //   });
  }
  const anchorLinks = [
    { href: 'form-a1', title: '申请时间' },
    { href: 'form-a2', title: '审核截止时间' },
    { href: 'form-a3', title: '公示信息' },
    { href: 'form-a4', title: '审核流程设置' },
    { href: 'form-a5', title: '申请表打印模板' },
    { href: 'form-a6', title: '学生申请表打印流程' },
  ];
  const anchorKey = ref('form-a1');
  const wrapperRef = ref<ComponentRef>(null);
  const anchor = ref(null);
  let isClickTriggered = false;
  let debounceTimer = null; // 防抖定时器

  const getContainer = () => {
    return wrapperRef.value.$el;
  };
  const handleClickAnchor = link => {
    isClickTriggered = true;
    anchorKey.value = link;
    const element = document.getElementById(link);
    element && element.scrollIntoView({ behavior: 'smooth' });
    setTimeout(() => {
      isClickTriggered = false;
    }, 300);
  };

  const observer = new IntersectionObserver(
    entries => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const groupId = entry.target.getAttribute('id');
          if (!isClickTriggered) {
            if (debounceTimer) {
              clearTimeout(debounceTimer);
            }
            debounceTimer = setTimeout(() => {
              anchorKey.value = groupId;
            }, 200);
          }
        }
      });
    },
    {
      root: wrapperRef.value,
      rootMargin: '0px',
      threshold: 0.5,
    },
  );
  const groupTitles = ref();
  onMounted(() => {
    setTimeout(() => {
      groupTitles.value = document.querySelectorAll('[id^="form-a"]');
      groupTitles.value.forEach(title => {
        observer.observe(title);
      });
    }, 10);
  });

  onUnmounted(() => {
    groupTitles.value.forEach(title => {
      observer.unobserve(title);
    });
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }
  });
</script>
<style scoped lang="less">
  .pages {
    width: 90%;
    min-width: 600px;
    overflow: hidden;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 200px;
    grid-gap: 20px;
  }
  .anchorGroup {
    position: fixed;
    right: 0;
    padding: 20px;

    transition: color 0.3s;
    .anchorGroup-item {
      color: rgba(0, 0, 0, 0.88);
      font-size: 14px;
      height: 30px;
      line-height: 30px;
      padding-left: 20px;
      border-left: 2px solid #f0f0f0;
      cursor: pointer;
    }
    .anchorActive {
      color: @primary-color;
      border-left: 2px solid @primary-color;
    }
  }
</style>
